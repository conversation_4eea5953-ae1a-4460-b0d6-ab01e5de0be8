/* eslint-disable react/require-default-props */
import React, {
  useState, forwardRef,
} from 'react';
import { Box, CircularProgress } from '@mui/material';

const DEFAULT_SANDBOX = 'allow-same-origin allow-scripts allow-forms allow-popups';

export interface SecureIframeProps extends React.IframeHTMLAttributes<HTMLIFrameElement> {
  src: string;
  title?: string;
  className?: string;
  style?: React.CSSProperties;
  showLoading?: boolean;
  onLoad?: () => void;
  sandbox?: string;
}

const SecureIframe = forwardRef<HTMLIFrameElement, SecureIframeProps>(({
  src,
  title = '',
  className = 'secure-iframe-wrap',
  style = {},
  showLoading = true,
  onLoad,
  sandbox = DEFAULT_SANDBOX,
  ...rest
}, ref) => {
  const [status, setStatus] = useState<'loading' | 'loaded' | 'error'>('loading');

  const handleLoad = () => {
    setStatus('loaded');
    onLoad?.();
  };

  return (
    <Box
      className={className}
    >
      {status === 'loading' && showLoading && (
        <div data-testid="loader">
          <CircularProgress size={24} />
        </div>
      )}

      <iframe
        ref={ref}
        src={src}
        title={title}
        style={{
          ...style,
        }}
        sandbox={sandbox}
        allowFullScreen
        onLoad={handleLoad}
        {...rest}
      />
    </Box>
  );
});

SecureIframe.displayName = 'SecureIframe';

export default SecureIframe;
